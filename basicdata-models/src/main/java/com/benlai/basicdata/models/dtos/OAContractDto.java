package com.benlai.basicdata.models.dtos;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OAContractDto {
    /**
     * 合同金额浮动比例
     */
    private Integer amountFloatingRatio;
    /**
     * 自动顺延条款日期
     */
    private String autoDelayDay;
    /**
     * 收取保证金额
     */
    private BigDecimal confirmFeeAmount;
    /**
     * 关联合同编号（框架合同）
     */
    private String connectContractNo;
    /**
     * 关联邮件合同编号
     */
    private String connectEmailContractNo;

    private String connectEmailContractName;
    /**
     * 合同期限 结束日期
     */
    private String contractEndTime;
    /**
     * 合同期限 开始日期
     */
    private String contractStartTime;
    /**
     * 合同标题
     */
    private String contractTitle;
    /**
     * 合同金额、销售金额、授信金额
     */
    private BigDecimal contractTotalAmount;
    /**
     * 合同性质
     */
    private Integer contractType;
    /**
     * 授信额度
     */
    private Integer creditAmount;
    /**
     * 实物商品授信比例
     */
    private String creditProductRatio;
    /**
     * 是否收取保证金
     */
    private Integer hasConfirmFee;
    /**
     * 是否有自动顺延条款选项
     */
    private Integer hasDelayClause;
    /**
     * 发票类型 0：是。1：否。 0增值税普通发票  1增值税普专票
     */
    private Integer invoiceType;
    /**
     * 是否使用电子合同
     */
    private Integer isElectronicContract;
    /**
     * 是否签订过邮件合同
     */
    private Integer isEmailConfirm;
    /**
     * 是否开具发票  0开具  1 不开具
     */
    private Integer isIssueInvoice;
    /**
     * 后续是否签订纸质合同
     */
    private Integer isNeedPaperContract;
    /**
     * 是否需双方盖章
     */
    private Integer isNeedSeal;
    /**
     * 是否模板合同
     */
    private Integer isTemplateContract;
    /**
     * 滞纳金条款
     */
    private Integer lateFee;
    /**
     * 条款说明
     */
    private String lateFeeClause;
    /**
     * 是否支付保证金
     */
    private Integer needPayConfirmFee;
    /**
     * 已支付保证金额
     */
    private BigDecimal needPayConfirmFeeAmt;

    /**
     * 订单形式
     */
    private List<Integer> orderType;
    /**
     * 销售类型
     */
    private Integer salesType;
    /**
     * 服务商品授信比例
     */
    private String serviceProductRatio;


    private String submitPlatform;
    /*
    *   额度发放方式
    * */
    private Integer creditRecType;
}
