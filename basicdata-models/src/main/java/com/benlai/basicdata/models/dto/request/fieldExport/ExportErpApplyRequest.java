package com.benlai.basicdata.models.dto.request.fieldExport;

import java.util.List;

import lombok.Data;

/**
 * ERP字段导出申请
 */
@Data
public class ExportErpApplyRequest {
    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 模板code
     */
    private String template;

    /**
     * 查询条件
     */
    private String queryJson;

    /**
     * 导出类型
     *
     * @see com.benlai.basicdata.models.dto.enums.FieldExportTypeEnum
     */
    private Integer type;

    /**
     * 导出字段
     */
    private List<Long> fields;

    /**
     * 用户sysNo
     */
    private Integer userSysNo;
}
