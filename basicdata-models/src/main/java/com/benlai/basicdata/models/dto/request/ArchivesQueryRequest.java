package com.benlai.basicdata.models.dto.request;

import com.benlai.basicdata.models.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ArchivesQueryRequest extends PageRequest {
    /**
     * 档案名称
     */
    private String archivesName;

    /**
     * 客户行业
     */
    private Integer customerIndustryNo;

    /**
     * 客户开始时间
     */
    private LocalDate compactStartTime;

    /**
     * 合同结束时间
     */
    private LocalDate compactEndTime;

    /**
     * 最小总金额
     */
    private BigDecimal minAmount;

    /**
     * 最大总金额
     */
    private BigDecimal maxAmount;

    /**
     * 销售类型
     */
    private Integer salesType;

    /**
     * 签约主体
     */
    private String companyNo;

    /**
     * 是否定制
     */
    private String customMade;

    /**
     * 是否上传发票
     */
    private String uploadInvoice;

    /**
     * 是否上传中标通知书
     */
    private String noticeOfWinningTheBid;

    /**
     * 福利类型/购买用途
     */
    private Integer purchasePurpose;

    /**
     * 商品大类
     */
    private Integer c1SysNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 排序规则
     */
    private Integer sortRule;

    /**
     * 发布状态
     */
    private Integer publishStatus;

    /**
     * 合同编号
     */
    private String compactNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     *
     */
    private List<Integer> idList;

    /**
     *
     */
    private List<Integer> customerIndustryNoList;
}
