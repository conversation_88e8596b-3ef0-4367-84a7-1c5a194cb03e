package com.benlai.asset.flow.data.entity;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "asset_flow")
public class AssetFlow extends PanacheEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "flow_no", nullable = false, length = 64)
    private String flowNo;

    @Column(name = "company_no", nullable = false, length = 20)
    private String companyNo;

    @Column(name = "asset_type", nullable = false, length = 20)
    private String assetType;

    @Column(name = "asset_no", nullable = false, length = 40)
    private String assetNo;

    @Column(name = "third_biz_type_code", nullable = false, length = 10)
    private String thirdBizTypeCode;

    @Column(name = "third_biz_type_name",  length = 30)
    private String thirdBizTypeName;

    @Column(name = "biz_type_code", nullable = false, length = 10)
    private String bizTypeCode;

    @Column(name = "biz_type_name", nullable = false, length = 30)
    private String bizTypeName;

    @Column(name = "paper_no", nullable = false, length = 40)
    private String paperNo;

    @Column(name = "qty", nullable = false, precision = 12, scale = 3)
    private BigDecimal qty;

    @Column(name = "amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal amount;

    @Column(name = "biz_time", nullable = false)
    private Instant bizTime;

    @Column(name = "flow_create_time", nullable = false)
    private Instant flowCreateTime;

    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    @Column(name = "create_eid", nullable = false, length = 10)
    private String createEid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFlowNo() {
        return flowNo;
    }

    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public String getAssetNo() {
        return assetNo;
    }

    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo;
    }

    public String getThirdBizTypeCode() {
        return thirdBizTypeCode;
    }

    public void setThirdBizTypeCode(String thirdBizTypeCode) {
        this.thirdBizTypeCode = thirdBizTypeCode;
    }

    public String getThirdBizTypeName() {
        return thirdBizTypeName;
    }

    public void setThirdBizTypeName(String thirdBizTypeName) {
        this.thirdBizTypeName = thirdBizTypeName;
    }

    public String getBizTypeCode() {
        return bizTypeCode;
    }

    public void setBizTypeCode(String bizTypeCode) {
        this.bizTypeCode = bizTypeCode;
    }

    public String getBizTypeName() {
        return bizTypeName;
    }

    public void setBizTypeName(String bizTypeName) {
        this.bizTypeName = bizTypeName;
    }

    public String getPaperNo() {
        return paperNo;
    }

    public void setPaperNo(String paperNo) {
        this.paperNo = paperNo;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Instant getBizTime() {
        return bizTime;
    }

    public void setBizTime(Instant bizTime) {
        this.bizTime = bizTime;
    }

    public Instant getFlowCreateTime() {
        return flowCreateTime;
    }

    public void setFlowCreateTime(Instant flowCreateTime) {
        this.flowCreateTime = flowCreateTime;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public String getCreateEid() {
        return createEid;
    }

    public void setCreateEid(String createEid) {
        this.createEid = createEid;
    }

}