package com.benlai.asset.flow.data.repository;

import com.benlai.asset.flow.data.entity.AssetDaily;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class AssetDailyRepository implements PanacheRepository<AssetDaily> {

    public Long insert(AssetDaily assetDaily) {
        persist(assetDaily);
        return assetDaily.getId();
    }


     public List<AssetDaily> query(String assetNo, Integer pageNo, Integer pageSize){

        PanacheQuery<AssetDaily> q = AssetDaily.find("assetNo", assetNo);
         if(pageNo  < 1 || pageSize < 1){
             return q.list();
         }else{
             Long count = q.count();
             return q.page(Page.of(pageNo, pageSize)).list();
         }

     }




}
