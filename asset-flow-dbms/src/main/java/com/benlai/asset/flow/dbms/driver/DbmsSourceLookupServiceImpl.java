package com.benlai.asset.flow.dbms.driver;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

interface DbmsConfigApiService{
    RawConnectionConfig fetchConnectionConfig(String lookupKey);
}
/**
 * 服务接口，用于根据查找键动态获取真实的数据库驱动配置。
 * 您需要提供此接口的实现。
 */
 interface DbmsSourceLookupService {

    /**
     * 根据给定的查找键获取真实的驱动配置。
     *
     * @param lookupKey 从自定义JDBC URL中解析出的键，用于查找特定数据库的配置。
     * @return 包含真实驱动类名、JDBC URL、用户名、密码和其他属性的配置对象；
     *         如果找不到配置，则返回 null。
     */
    RealDriverConfig getRealDriverConfig(String lookupKey);
}


record RawConnectionConfig(String driverClassName, String url, String username, String password, Map<String,String> properties){}

public class DbmsSourceLookupServiceImpl implements DbmsSourceLookupService {

    private static final Logger LOGGER = Logger.getLogger(DbmsSourceLookupServiceImpl.class.getName());
    private final DbmsConfigApiService dbmsConfigApiService;



    public DbmsSourceLookupServiceImpl(DbmsConfigApiService configApiService) {
        this.dbmsConfigApiService = configApiService;
        if(this.dbmsConfigApiService == null){
            throw new IllegalArgumentException("dbmsConfigApiService cannot be null");
        }
    }

    public DbmsSourceLookupServiceImpl(){
        this.dbmsConfigApiService = createDbmsConfigApiService();

    }

    private DbmsConfigApiService createDbmsConfigApiService(){
        return new DbmsConfigApiService() {
            private final HttpClient httpClient = HttpClient.newBuilder().build();

            @Override
            public RawConnectionConfig fetchConnectionConfig(String lookupKey) {
                String url = "";
                String requestUrl = url.endsWith("/") ? url : url + "/";
                HttpRequest request = HttpRequest.newBuilder().uri(URI.create(requestUrl)).GET().build();
                try{
                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                    if(response.statusCode() == 200){

                        String responseBody = response.body();
                        // 解析响应体并返回 RawConnectionConfig
                        if(responseBody != null && !responseBody.isEmpty()){
                            // 解析 JSON 或其他格式的响应体
                            // 返回 RawConnectionConfig
                            return new RawConnectionConfig(
                                    "com.mysql.cj.jdbc.Driver",
                                    "********************************",
                                    "root",
                                    "password",
                                    Map.of());
                        }
                    }
                }catch (Exception e){
                    LOGGER.severe("Failed to fetch connection config: " + e.getMessage());
                }
                return null;
            }
        };
    }

    @Override
    public RealDriverConfig getRealDriverConfig(String lookupKey) {
        if(this.dbmsConfigApiService == null){
            throw new IllegalStateException("dbmsConfigApiService is not initialized");
        }
        try{
            RawConnectionConfig rawConfig = this.dbmsConfigApiService.fetchConnectionConfig(lookupKey);
            if(rawConfig == null){
                return null;
            }
            Properties properties = null;
            if(rawConfig.properties() != null){
                properties = new Properties();
                properties.putAll(rawConfig.properties());
            }
            return new RealDriverConfig(rawConfig.driverClassName(),rawConfig.url(),rawConfig.username(),rawConfig.password(),properties);
        }catch (Exception e){
            LOGGER.log(Level.SEVERE, "Error fetching connection config: " + lookupKey, e);
            return null;
        }
    }
}

/**
 * 存储真实JDBC驱动配置的数据记录。
 *
 * @param driverClassName 实际驱动的完整类名
 * @param realJdbcUrl     实际的JDBC连接URL
 * @param username        连接数据库的用户名 (可选)
 * @param password        连接数据库的密码 (可选)
 * @param connectionProperties 额外的连接属性 (可选)
 */
 record RealDriverConfig(
        String driverClassName,
        String realJdbcUrl,
        String username,
        String password,
        Properties connectionProperties
) {
    /**
     * 规范构造函数，可以添加验证或默认值处理。
     * 这里我们确保 connectionProperties 永远不为 null，并创建一个防御性副本。
     */
    public RealDriverConfig {
        if (driverClassName == null || driverClassName.isBlank()) {
            throw new IllegalArgumentException("driverClassName cannot be null or blank");
        }
        if (realJdbcUrl == null || realJdbcUrl.isBlank()) {
            throw new IllegalArgumentException("realJdbcUrl cannot be null or blank");
        }
        // 创建 connectionProperties 的防御性副本，确保不可变性且不为null
        connectionProperties = (connectionProperties != null) ? new Properties(connectionProperties) : new Properties();
    }

    /**
     * 如果需要一个不包含所有参数的便捷构造函数，可以像这样添加。
     * 例如，一个没有额外连接属性的构造函数。
     */
    public RealDriverConfig(String driverClassName, String realJdbcUrl, String username, String password) {
        this(driverClassName, realJdbcUrl, username, password, null);
    }


}
