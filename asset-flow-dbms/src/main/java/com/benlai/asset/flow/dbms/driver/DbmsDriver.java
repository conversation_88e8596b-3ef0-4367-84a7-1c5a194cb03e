package com.benlai.asset.flow.dbms.driver;

import java.sql.*;
import java.util.Iterator;
import java.util.Properties;
import java.util.ServiceLoader;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DbmsDriver implements Driver {

    private static final Logger LOGGER = Logger.getLogger(DbmsDriver.class.getName());
    private static  final String URL_PREFIX = "jdbc:dbms";
    private transient volatile DbmsSourceLookupService dynamicDataSourceLookupService;

    static {
        try {
            DriverManager.registerDriver(new DbmsDriver());
        } catch (SQLException e) {
            LOGGER.severe("Failed to register DBMS driver: " + e.getMessage());
        }
    }

    private DbmsSourceLookupService getLoockupService() {
        if (dynamicDataSourceLookupService == null) {
            synchronized (this) {
                if (dynamicDataSourceLookupService == null) {
                    ServiceLoader<DbmsSourceLookupService> loader = ServiceLoader.load(DbmsSourceLookupService.class);
                    Iterator<DbmsSourceLookupService> iterator = loader.iterator();
                    if(iterator.hasNext()){
                        dynamicDataSourceLookupService = iterator.next();
                    }else{
                        LOGGER.log(Level.SEVERE, "No DbmsSourceLookupService implementation found");
                    }
                }
            }
        }
        return dynamicDataSourceLookupService;
    }

    @Override
    public Connection connect(String url, Properties info) throws SQLException {
        if(!acceptsURL(url)){
            return null;
        }
        DbmsSourceLookupService lookupService = getLoockupService();
        if(lookupService == null){
            throw new SQLException("No DbmsSourceLookupService implementation found");
        }
        String lookupKey = parseLookupKey(url);
        RealDriverConfig config = lookupService.getRealDriverConfig(lookupKey);
        if(config == null){
            throw new SQLException("No real driver config found for " + lookupKey);
        }
        try{
            Class.forName(config.driverClassName());
            Properties finalProps = new Properties();
            if(info != null){
                finalProps.putAll(info);
            }
            if(config.connectionProperties() != null){
                finalProps.putAll(config.connectionProperties());
            }
            return DriverManager.getConnection(config.realJdbcUrl(), finalProps);
        }catch (Exception e){
            throw new SQLException("Failed to load driver class: " + e.getMessage());
        }
    }

    private String parseLookupKey(String url){
        String pathPart = url.substring(URL_PREFIX.length());
        if(pathPart.startsWith("//")){
            pathPart = pathPart.substring(2);
        }
        int slashIndex = pathPart.indexOf('/');
        if(slashIndex == -1){
            return pathPart.substring(0, slashIndex);
        }
        return pathPart;
    }

    @Override
    public boolean acceptsURL(String url) throws SQLException {
        return url!=null && url.startsWith(URL_PREFIX);
    }

    @Override
    public DriverPropertyInfo[] getPropertyInfo(String url, Properties info) throws SQLException {
        return new DriverPropertyInfo[0];
    }

    @Override
    public int getMajorVersion() {
        return 1;
    }

    @Override
    public int getMinorVersion() {
        return 0;
    }

    @Override
    public boolean jdbcCompliant() {
        return false;
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return null;
    }
}
