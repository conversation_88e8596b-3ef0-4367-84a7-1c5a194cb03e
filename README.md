# 进销存系统 (Asset Flow Management)

基于 Quarkus 框架和 JDK 24 构建的现代化进销存管理系统主模块。

## 前置要求

- JDK 24 或更高版本
- Maven 4.0.0-rc-3 或更高版本（推荐使用项目内置的 Maven Wrapper）

## 项目结构

```
asset-flow-parent/
├── pom.xml                    # 主模块 POM 文件
├── README.md                  # 项目说明
├── .gitignore                 # Git 忽略文件
├── mvnw / mvnw.cmd           # Maven Wrapper 脚本
└── 子模块/
    ├── asset-flow-api/         # REST & gRPC API 接口模块
    ├── asset-flow-data/        # JPA 数据访问层模块
    ├── asset-flow-dbms/        # 数据库客户端驱动模块
    └── asset-flow-dto/         # 数据传输对象模块
```

## 模块说明

### asset-flow-api
- **功能**: 提供 REST API 和 gRPC 接口
- **技术栈**: Quarkus REST, Jackson, CDI
- **端口**: 8080 (HTTP), 9000 (gRPC)

### asset-flow-data
- **功能**: 数据访问层，包含实体类和仓储接口
- **技术栈**: Hibernate ORM, Panache

### asset-flow-dbms
- **功能**: 数据库客户端驱动和连接管理
- **技术栈**: JDBC, 连接池

### asset-flow-dto
- **功能**: 数据传输对象，用于 API 请求和响应
- **技术栈**: Jackson 注解, Java Records

### asset-flow-core
- **功能**: 核心业务逻辑，对象映射，服务层
- **技术栈**: MapStruct, CDI, Jakarta Validation


## 技术栈
- **JDK 24**: 支持最新的 Java 特性，启用预览功能
- **Quarkus 框架**: 快速启动和低内存占用
- **多模块架构**: 清晰的模块分离，便于维护和扩展
- **统一版本管理**: 通过 dependencyManagement 统一管理依赖版本

## 开始使用

### Maven Wrapper

项目包含 Maven Wrapper，确保所有开发者使用相同的 Maven 版本（3.9.9）：

**注意**: 由于 Maven 4.0.0-rc-3 与 Quarkus 3.15.1 存在兼容性问题，我们使用 Maven 3.9.9 作为 wrapper 版本。

**Windows:**
```cmd
# 查看 Maven 版本
.\mvnw.cmd --version

# 构建项目
.\mvnw.cmd clean compile

# 打包项目
.\mvnw.cmd clean package

# 运行 API 模块
cd asset-flow-api
..\mvnw.cmd clean package -DskipTests
java -jar target/quarkus-app/quarkus-run.jar
```

**Unix/Linux/macOS:**
```bash
# 查看 Maven 版本
./mvnw --version

# 构建项目
./mvnw clean compile

# 打包项目
./mvnw clean package

# 运行 API 模块
cd asset-flow-api
../mvnw clean package -DskipTests
java -jar target/quarkus-app/quarkus-run.jar
```

**临时解决方案（如果 wrapper 有问题）:**
```cmd
# 直接使用系统 Maven
cd asset-flow-api
mvn clean package -DskipTests
java -jar target/quarkus-app/quarkus-run.jar
```

### 创建子模块

在主模块目录下创建子模块：

```bash
# 使用 Maven Wrapper 创建核心业务模块
.\mvnw.cmd archetype:generate -DgroupId=com.benlai -DartifactId=asset-fow-core -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false

# 创建 API 模块
.\mvnw.cmd archetype:generate -DgroupId=com.benlai -DartifactId=asset-fow-api -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false

# 创建服务模块
.\mvnw.cmd archetype:generate -DgroupId=com.benlai -DartifactId=asset-fow-service -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false

# 创建 Web 模块
.\mvnw.cmd archetype:generate -DgroupId=com.benlai -DartifactId=asset-fow-web -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false
```

### 构建项目

```bash
# 构建所有模块
.\mvnw.cmd clean compile

# 打包所有模块
.\mvnw.cmd clean package

# 运行测试
.\mvnw.cmd test
```

### 添加子模块到主 POM

在 `pom.xml` 的 `<modules>` 部分添加新创建的子模块：

```xml
<modules>
    <module>asset-fow-core</module>
    <module>asset-fow-api</module>
    <module>asset-fow-service</module>
    <module>asset-fow-web</module>
</modules>
```

## 开发指南

### 依赖管理

主模块通过 `dependencyManagement` 统一管理依赖版本，子模块只需要指定 groupId 和 artifactId，版本会自动继承。

### 插件管理

主模块通过 `pluginManagement` 统一配置插件，确保所有子模块使用相同的构建配置。

### 添加 Quarkus 扩展

在子模块中添加 Quarkus 扩展：
```bash
cd [子模块目录]
mvn quarkus:add-extension -Dextensions="extension-name"
```

## 推荐的模块结构

- **asset-fow-core**: 核心业务逻辑、实体类、工具类
- **asset-fow-api**: REST API 接口定义
- **asset-fow-service**: 业务服务层实现
- **asset-fow-web**: Web 前端或主应用程序入口

## License

This project is licensed under the MIT License.
