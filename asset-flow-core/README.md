# Asset Flow Core 模块

asset-flow-core 是进销存系统的核心业务模块，负责处理业务逻辑和对象映射。

## 功能特性

- **MapStruct 对象映射**: 自动生成高性能的对象映射代码
- **DTO 转换**: 支持请求 DTO 到实体、实体到响应 DTO 的转换
- **类型转换**: 自动处理 Double ↔ BigDecimal、String ↔ Instant 等类型转换
- **验证支持**: 集成 Jakarta Bean Validation
- **CDI 集成**: 支持 Quarkus CDI 依赖注入

## 模块结构

```
asset-flow-core/
├── src/main/java/com/benlai/asset/flow/core/
│   ├── dto/                    # 响应 DTO
│   │   ├── AssetFlowResponse.java
│   │   ├── AssetDailyResponse.java
│   │   └── AssetMonthlyResponse.java
│   ├── mapper/                 # MapStruct 映射器
│   │   ├── AssetFlowMapper.java
│   │   ├── AssetDailyMapper.java
│   │   ├── AssetMonthlyMapper.java
│   │   └── AssetMapperFactory.java
│   └── service/                # 业务服务
│       └── AssetFlowService.java
└── src/test/java/              # 测试代码
    └── com/benlai/asset/flow/core/mapper/
        └── AssetFlowMapperTest.java
```

## 依赖关系

- **asset-flow-dto**: 请求 DTO 定义
- **asset-flow-data**: 实体类和数据访问层
- **MapStruct**: 对象映射框架
- **Jackson**: JSON 序列化支持
- **Jakarta Validation**: 数据验证

## 使用示例

### 1. 基本映射使用

```java
@Inject
private AssetFlowMapper assetFlowMapper;

// 请求 DTO 转实体
AssetRequest request = new AssetRequest(
    "COMP001", "ASSET001", "COMPUTER", 
    "01", "03", "PAPER001", 
    100.5, 5000.99, "2024-01-15 10:30:00"
);
AssetFlow entity = assetFlowMapper.toEntity(request);

// 实体转响应 DTO
AssetFlowResponse response = assetFlowMapper.toResponse(entity);
```

### 2. 服务层使用

```java
@Inject
private AssetFlowService assetFlowService;

// 创建资产流水
AssetFlowResponse response = assetFlowService.createAssetFlow(request);

// 更新资产流水
AssetFlowResponse updated = assetFlowService.updateAssetFlow(id, request);

// 查询资产流水
AssetFlowResponse found = assetFlowService.getAssetFlow(id);
```

### 3. 映射器工厂使用

```java
@Inject
private AssetMapperFactory mapperFactory;

// 获取不同的映射器
AssetFlowMapper flowMapper = mapperFactory.getAssetFlowMapper();
AssetDailyMapper dailyMapper = mapperFactory.getAssetDailyMapper();
AssetMonthlyMapper monthlyMapper = mapperFactory.getAssetMonthlyMapper();
```

## JSON 映射示例

### 请求 JSON → AssetRequest DTO → AssetFlow 实体

```json
{
  "company_no": "COMP001",
  "asset_no": "ASSET001",
  "asset_type": "COMPUTER",
  "biz_type_code": "01",
  "third_biz_type_code": "03",
  "paper_no": "PAPER001",
  "qty": 100.5,
  "amount": 5000.99,
  "biz_time": "2024-01-15 10:30:00"
}
```

### AssetFlow 实体 → AssetFlowResponse DTO → 响应 JSON

```json
{
  "id": 1,
  "flow_no": "AF1705123456789abcd",
  "company_no": "COMP001",
  "asset_no": "ASSET001",
  "asset_type": "COMPUTER",
  "biz_type_code": "01",
  "biz_type_name": "采购入库",
  "third_biz_type_code": "03",
  "third_biz_type_name": "调拨入库",
  "paper_no": "PAPER001",
  "qty": 100.5,
  "amount": 5000.99,
  "biz_time": "2024-01-15T10:30:00Z",
  "flow_create_time": "2024-01-15T10:30:00Z",
  "create_time": "2024-01-15T10:30:00Z",
  "create_eid": "USER001"
}
```

## 类型转换说明

### 1. 数值类型转换
- `Double` → `BigDecimal`: 自动转换，保持精度
- `null` 值处理: 安全转换，避免 NPE

### 2. 日期时间转换
- 支持 ISO 格式: `2024-01-15T10:30:00Z`
- 支持常见格式: `2024-01-15 10:30:00`
- 支持日期格式: `2024-01-15`
- 错误处理: 无效格式抛出 `IllegalArgumentException`

### 3. 字段映射
- 使用 `@JsonProperty` 注解处理 JSON 字段名映射
- 支持驼峰命名与下划线命名转换
- 忽略不需要映射的字段

## 扩展指南

### 添加新的映射器

1. 创建响应 DTO
2. 创建 MapStruct 映射器接口
3. 在 AssetMapperFactory 中注册
4. 编写单元测试

### 自定义类型转换

```java
@Named("customConverter")
default TargetType customConverter(SourceType source) {
    // 自定义转换逻辑
    return convertedValue;
}
```

## 测试

运行测试：
```bash
mvn test
```

查看测试覆盖率：
```bash
mvn jacoco:report
```

## 注意事项

1. **性能**: MapStruct 在编译时生成映射代码，运行时性能优异
2. **类型安全**: 编译时检查映射的正确性
3. **空值处理**: 配置了 `NullValuePropertyMappingStrategy.IGNORE`
4. **CDI 集成**: 使用 `componentModel = "cdi"` 支持依赖注入
5. **预览功能**: 项目使用 JDK 24 预览功能，需要 `--enable-preview` 参数
