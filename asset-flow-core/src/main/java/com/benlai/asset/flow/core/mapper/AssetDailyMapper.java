package com.benlai.asset.flow.core.mapper;

import com.benlai.asset.flow.data.entity.AssetDaily;
import com.benlai.asset.flow.core.dto.AssetDailyResponse;
import org.mapstruct.*;

/**
 * 资产日报映射器
 * 负责 AssetDaily 和 AssetDailyResponse 之间的转换
 */
@Mapper(
    componentModel = "cdi",
    unmappedTargetPolicy = ReportingPolicy.WARN
)
public interface AssetDailyMapper {

    /**
     * 将实体转换为响应 DTO
     * @param entity 资产日报实体
     * @return 资产日报响应
     */
    AssetDailyResponse toResponse(AssetDaily entity);

    /**
     * 批量转换
     * @param entities 实体列表
     * @return 响应 DTO 列表
     */
    java.util.List<AssetDailyResponse> toResponseList(java.util.List<AssetDaily> entities);
}
