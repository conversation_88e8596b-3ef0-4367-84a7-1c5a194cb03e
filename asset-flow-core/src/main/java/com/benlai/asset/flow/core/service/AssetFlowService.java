package com.benlai.asset.flow.core.service;

import com.benlai.asset.flow.data.entity.AssetFlow;
import com.benlai.asset.flow.dto.AssetRequest;
import com.benlai.asset.flow.core.mapper.AssetFlowMapper;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.Instant;
import java.util.UUID;

/**
 * 资产流水服务
 * 演示如何使用 MapStruct 映射器
 */
@ApplicationScoped
public class AssetFlowService {

    @Inject
    private AssetFlowMapper assetFlowMapper;

    /**
     * 创建资产流水
     * @param request 请求 DTO
     * @return 响应 DTO
     */
    @Transactional
    public Long createAssetFlow(AssetRequest request) {
        // 使用映射器将请求转换为实体
        AssetFlow entity = assetFlowMapper.toEntity(request);
        
        // 设置业务逻辑字段
        if(request.flowCreateTime() == null || request.flowCreateTime().equals(Instant.EPOCH)){
            entity.setFlowCreateTime(Instant.now());
        }
        if(request.bizTime() == null || request.bizTime().equals(Instant.EPOCH)){
            entity.setBizTime(Instant.now());
        }
        entity.setCreateTime(Instant.now());
        entity.setCreateEid("SYSTEM"); //
        
        // 根据业务类型代码查询业务类型名称（示例）
        entity.setBizTypeName(getBizTypeName(request.bizTypeCode()));
        if (request.thirdBizTypeCode() != null) {
            entity.setThirdBizTypeName(getBizTypeName(request.thirdBizTypeCode()));
        }
        
        // 持久化实体
        entity.persist();
        
        return entity.getId();
    }



    /**
     * 生成流水号
     */
    private String generateFlowNo() {
        return "AF" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 根据业务类型代码获取业务类型名称
     * 实际应该从数据库或缓存中查询
     */
    private String getBizTypeName(String bizTypeCode) {
        // 这里是示例实现，实际应该从数据字典或配置中获取
        return switch (bizTypeCode) {
            case "01" -> "采购入库";
            case "02" -> "销售出库";
            case "03" -> "调拨入库";
            case "04" -> "调拨出库";
            case "05" -> "盘盈入库";
            case "06" -> "盘亏出库";
            default -> "未知类型";
        };
    }
}
