package com.benlai.asset.flow.core.mapper;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * 资产映射器工厂
 * 提供统一的映射器访问入口
 */
@ApplicationScoped
public class AssetMapperFactory {

    @Inject
    private AssetFlowMapper assetFlowMapper;

    @Inject
    private AssetDailyMapper assetDailyMapper;

    @Inject
    private AssetMonthlyMapper assetMonthlyMapper;

    /**
     * 获取资产流水映射器
     */
    public AssetFlowMapper getAssetFlowMapper() {
        return assetFlowMapper;
    }

    /**
     * 获取资产日报映射器
     */
    public AssetDailyMapper getAssetDailyMapper() {
        return assetDailyMapper;
    }

    /**
     * 获取资产月报映射器
     */
    public AssetMonthlyMapper getAssetMonthlyMapper() {
        return assetMonthlyMapper;
    }
}
