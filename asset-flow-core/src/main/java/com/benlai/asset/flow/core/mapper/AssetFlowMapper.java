package com.benlai.asset.flow.core.mapper;

import com.benlai.asset.flow.data.entity.AssetFlow;
import com.benlai.asset.flow.dto.AssetRequest;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 资产流水映射器
 * 负责 AssetRequest、AssetFlow 和 AssetFlowResponse 之间的转换
 */
@Mapper(
    componentModel = "cdi",
    unmappedTargetPolicy = ReportingPolicy.WARN,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface AssetFlowMapper {

    /**
     * 将请求 DTO 转换为实体
     * @param request 资产流水请求
     * @return 资产流水实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "flowNo", source = "flowNo") // 流水号由业务逻辑生成
    @Mapping(target = "thirdBizTypeCode", source = "thirdBizTypeCode")
    @Mapping(target = "bizTypeCode", source = "bizTypeCode")
    @Mapping(target = "thirdBizTypeName", ignore = true) // 需要通过业务逻辑查询
    @Mapping(target = "bizTypeName", ignore = true) // 需要通过业务逻辑查询
    @Mapping(target = "qty", source = "qty", qualifiedByName = "doubleToDecimal")
    @Mapping(target = "amount", source = "amount", qualifiedByName = "doubleToDecimal")
    @Mapping(target = "bizTime", source = "bizTime", qualifiedByName = "stringToInstant")
    @Mapping(target = "flowCreateTime", source = "flowCreateTime", qualifiedByName = "stringToInstant") // 由业务逻辑设置
    @Mapping(target = "createTime", ignore = true) // 由业务逻辑设置
    @Mapping(target = "createEid", ignore = true) // 由业务逻辑设置
    AssetFlow toEntity(AssetRequest request);



    /**
     * 部分更新实体
     * @param request 请求 DTO
     * @param entity 现有实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "flowNo", ignore = true)
    @Mapping(target = "thirdBizTypeName", ignore = true)
    @Mapping(target = "bizTypeName", ignore = true)
    @Mapping(target = "qty", source = "qty", qualifiedByName = "doubleToDecimal")
    @Mapping(target = "amount", source = "amount", qualifiedByName = "doubleToDecimal")
    @Mapping(target = "bizTime", source = "bizTime", qualifiedByName = "stringToInstant")
    @Mapping(target = "flowCreateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createEid", ignore = true)
    void updateEntity(AssetRequest request, @MappingTarget AssetFlow entity);

    /**
     * Double 转 BigDecimal
     */
    @Named("doubleToDecimal")
    default BigDecimal doubleToDecimal(Double value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    /**
     * String 转 Instant
     * 支持多种日期时间格式
     */
    @Named("stringToInstant")
    default Instant stringToInstant(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试 ISO 格式
            return Instant.parse(dateTimeStr);
        } catch (DateTimeParseException e1) {
            try {
                // 尝试常见格式 yyyy-MM-dd HH:mm:ss
                return Instant.from(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    .parse(dateTimeStr));
            } catch (DateTimeParseException e2) {
                try {
                    // 尝试日期格式 yyyy-MM-dd
                    return Instant.from(DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .parse(dateTimeStr));
                } catch (DateTimeParseException e3) {
                    throw new IllegalArgumentException("无法解析日期时间格式: " + dateTimeStr, e3);
                }
            }
        }
    }
}
