package com.benlai.asset.flow.core.mapper;

import com.benlai.asset.flow.data.entity.AssetMonthly;
import com.benlai.asset.flow.core.dto.AssetMonthlyResponse;
import org.mapstruct.*;

/**
 * 资产月报映射器
 * 负责 AssetMonthly 和 AssetMonthlyResponse 之间的转换
 */
@Mapper(
    componentModel = "cdi",
    unmappedTargetPolicy = ReportingPolicy.WARN
)
public interface AssetMonthlyMapper {

    /**
     * 将实体转换为响应 DTO
     * @param entity 资产月报实体
     * @return 资产月报响应
     */
    AssetMonthlyResponse toResponse(AssetMonthly entity);

    /**
     * 批量转换
     * @param entities 实体列表
     * @return 响应 DTO 列表
     */
    java.util.List<AssetMonthlyResponse> toResponseList(java.util.List<AssetMonthly> entities);
}
