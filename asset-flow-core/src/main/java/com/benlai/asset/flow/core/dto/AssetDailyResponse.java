package com.benlai.asset.flow.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 资产日报响应 DTO
 * 用于返回给客户端的资产日报数据
 */
public record AssetDailyResponse(
        @JsonProperty("id")
        Long id,
        
        @JsonProperty("trade_date")
        LocalDate tradeDate,
        
        @JsonProperty("company_no")
        String companyNo,
        
        @JsonProperty("asset_type")
        String assetType,
        
        @JsonProperty("asset_no")
        String assetNo,
        
        @JsonProperty("biz_type_code")
        String bizTypeCode,
        
        @JsonProperty("biz_type_name")
        String bizTypeName,
        
        @JsonProperty("qty1")
        BigDecimal qty1,
        
        @JsonProperty("amount1")
        BigDecimal amount1,
        
        @JsonProperty("qty2")
        BigDecimal qty2,
        
        @JsonProperty("amount2")
        BigDecimal amount2,
        
        @JsonProperty("create_time")
        Instant createTime
) {
}
