package com.benlai.asset.flow.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 资产流水响应 DTO
 * 用于返回给客户端的资产流水数据
 */
public record AssetFlowResponse(
        @JsonProperty("id")
        Long id,
        
        @JsonProperty("flow_no")
        String flowNo,
        
        @JsonProperty("company_no")
        String companyNo,
        
        @JsonProperty("asset_type")
        String assetType,
        
        @JsonProperty("asset_no")
        String assetNo,
        
        @JsonProperty("third_biz_type_code")
        String thirdBizTypeCode,
        
        @JsonProperty("third_biz_type_name")
        String thirdBizTypeName,
        
        @JsonProperty("biz_type_code")
        String bizTypeCode,
        
        @JsonProperty("biz_type_name")
        String bizTypeName,
        
        @JsonProperty("paper_no")
        String paperNo,
        
        @JsonProperty("qty")
        BigDecimal qty,
        
        @JsonProperty("amount")
        BigDecimal amount,
        
        @JsonProperty("biz_time")
        Instant bizTime,
        
        @JsonProperty("flow_create_time")
        Instant flowCreateTime,
        
        @JsonProperty("create_time")
        Instant createTime,
        
        @JsonProperty("create_eid")
        String createEid
) {
}
