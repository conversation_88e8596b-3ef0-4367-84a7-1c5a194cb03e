package com.benlai.asset.flow.core.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 资产月报响应 DTO
 * 用于返回给客户端的资产月报数据
 */
public record AssetMonthlyResponse(
        @JsonProperty("id")
        Long id,
        
        @JsonProperty("trade_month")
        Integer tradeMonth,
        
        @JsonProperty("company_no")
        String companyNo,
        
        @JsonProperty("asset_type")
        String assetType,
        
        @JsonProperty("asset_no")
        String assetNo,
        
        @JsonProperty("biz_type_code")
        String bizTypeCode,
        
        @JsonProperty("biz_type_name")
        String bizTypeName,
        
        @JsonProperty("qty1")
        BigDecimal qty1,
        
        @JsonProperty("amount1")
        BigDecimal amount1,
        
        @JsonProperty("qty2")
        BigDecimal qty2,
        
        @JsonProperty("amount2")
        BigDecimal amount2,
        
        @JsonProperty("create_time")
        Instant createTime
) {
}
