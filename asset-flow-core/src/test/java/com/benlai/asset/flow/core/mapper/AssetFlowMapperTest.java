package com.benlai.asset.flow.core.mapper;

import com.benlai.asset.flow.data.entity.AssetFlow;
import com.benlai.asset.flow.dto.AssetRequest;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 资产流水映射器测试
 */
class AssetFlowMapperTest {

    private final AssetFlowMapper mapper = Mappers.getMapper(AssetFlowMapper.class);

    @Test
    void testToEntity() {
        // 准备测试数据
        AssetRequest request = new AssetRequest(
                "COMP001",
                "ASSET001", 
                "COMPUTER",
                "01",
                "03",
                "PAPER001",
                100.5,
                5000.99,
                "2024-01-15 10:30:00"
        );

        // 执行映射
        AssetFlow entity = mapper.toEntity(request);

        // 验证结果
        assertNotNull(entity);
        assertEquals("COMP001", entity.getCompanyNo());
        assertEquals("ASSET001", entity.getAssetNo());
        assertEquals("COMPUTER", entity.getAssetType());
        assertEquals("01", entity.getBizTypeCode());
        assertEquals("03", entity.getThirdBizTypeCode());
        assertEquals("PAPER001", entity.getPaperNo());
        assertEquals(BigDecimal.valueOf(100.5), entity.getQty());
        assertEquals(BigDecimal.valueOf(5000.99), entity.getAmount());
        assertNotNull(entity.getBizTime());
        
        // 验证忽略的字段
        assertNull(entity.getId());
        assertNull(entity.getFlowNo());
        assertNull(entity.getBizTypeName());
        assertNull(entity.getThirdBizTypeName());
    }

    @Test
    void testToResponse() {
        // 准备测试数据
        AssetFlow entity = new AssetFlow();
        entity.setId(1L);
        entity.setFlowNo("AF001");
        entity.setCompanyNo("COMP001");
        entity.setAssetNo("ASSET001");
        entity.setAssetType("COMPUTER");
        entity.setBizTypeCode("01");
        entity.setBizTypeName("采购入库");
        entity.setThirdBizTypeCode("03");
        entity.setThirdBizTypeName("调拨入库");
        entity.setPaperNo("PAPER001");
        entity.setQty(BigDecimal.valueOf(100.5));
        entity.setAmount(BigDecimal.valueOf(5000.99));
        entity.setBizTime(Instant.now());
        entity.setFlowCreateTime(Instant.now());
        entity.setCreateTime(Instant.now());
        entity.setCreateEid("USER001");

        // 执行映射
        AssetFlowResponse response = mapper.toResponse(entity);

        // 验证结果
        assertNotNull(response);
        assertEquals(1L, response.id());
        assertEquals("AF001", response.flowNo());
        assertEquals("COMP001", response.companyNo());
        assertEquals("ASSET001", response.assetNo());
        assertEquals("COMPUTER", response.assetType());
        assertEquals("01", response.bizTypeCode());
        assertEquals("采购入库", response.bizTypeName());
        assertEquals("03", response.thirdBizTypeCode());
        assertEquals("调拨入库", response.thirdBizTypeName());
        assertEquals("PAPER001", response.paperNo());
        assertEquals(BigDecimal.valueOf(100.5), response.qty());
        assertEquals(BigDecimal.valueOf(5000.99), response.amount());
        assertNotNull(response.bizTime());
        assertNotNull(response.flowCreateTime());
        assertNotNull(response.createTime());
        assertEquals("USER001", response.createEid());
    }

    @Test
    void testDoubleToDecimal() {
        // 测试正常值
        BigDecimal result = mapper.doubleToDecimal(123.45);
        assertEquals(BigDecimal.valueOf(123.45), result);

        // 测试 null 值
        BigDecimal nullResult = mapper.doubleToDecimal(null);
        assertNull(nullResult);
    }

    @Test
    void testStringToInstant() {
        // 测试 ISO 格式
        Instant result1 = mapper.stringToInstant("2024-01-15T10:30:00Z");
        assertNotNull(result1);

        // 测试 null 值
        Instant nullResult = mapper.stringToInstant(null);
        assertNull(nullResult);

        // 测试空字符串
        Instant emptyResult = mapper.stringToInstant("");
        assertNull(emptyResult);

        // 测试无效格式
        assertThrows(IllegalArgumentException.class, () -> {
            mapper.stringToInstant("invalid-date");
        });
    }
}
