# Asset Flow API Configuration
quarkus:
  application:
    name: asset-flow-api
    version: 1.0.0-SNAPSHOT
  
  # HTTP Configuration
  http:
    port: 8080
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,PUT,POST,DELETE,OPTIONS"
      headers: "accept, authorization, content-type, x-requested-with"
  
  # gRPC Configuration - Temporarily disabled
  # grpc:
  #   server:
  #     port: 9000
  #     host: 0.0.0.0
  #   clients:
  #     # Configure gRPC clients here if needed
  
  # CDI Configuration - 扫描其他模块的包
  arc:
    additional-bean-defining-annotations:
      - "jakarta.enterprise.context.ApplicationScoped"
    index-dependency:
      com.benlai:asset-flow-core:
        group-id: com.benlai
        artifact-id: asset-flow-core
      com.benlai:asset-flow-data:
        group-id: com.benlai
        artifact-id: asset-flow-data

  # Logging Configuration
  log:
    level: INFO
    category:
      "com.benlai.assetflow.api": DEBUG
      "com.benlai.asset.flow": DEBUG
      "io.grpc": INFO
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"
  


# Custom application properties
api:
  version: v1
  base-path: /api/v1
  grpc:
    enabled: true
    reflection: true

  quarkus:
    datasource:
      db-kind: "mysql"
      driver: "com.mysql.cj.jdbc.Driver"
      url: "*************************************************************"
      username: "db_admin"
      password: "BLSHbranch@DBA"


    hibernate-orm:
      schema-management:
        strategy:drop-and-create
      log:
        sql: true
  

