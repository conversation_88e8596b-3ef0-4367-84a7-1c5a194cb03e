package com.benlai.assetflow.api.rest;

import com.benlai.asset.flow.core.service.AssetFlowService;
import com.benlai.asset.flow.dto.AssetRequest;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.Map;

@Path("/api/v1/assets")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AssetResource {

    @Inject
    private AssetFlowService assetFlowService;



    @GET
    public Response getAllAssets(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        
        // TODO: Implement asset retrieval logic
        return Response.ok(Map.of(
                "message", "Assets retrieved successfully",
                "page", page,
                "size", size,
                "data", List.of()
        )).build();
    }

    @GET
    @Path("/{id}")
    public Response getAssetById(@PathParam("id") String id) {

        // TODO: Implement asset retrieval by ID logic
        return Response.ok(Map.of(
                "message", "Asset retrieved successfully",
                "id", id,
                "data", Map.of("id", id, "name", "Sample Asset")
        )).build();
    }

    @POST
    public Response createAsset(@Valid AssetRequest request) {

        try {
            if(request == null){
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                        .entity(Map.of(
                                "code", -1,
                                "message", "request is null"
                        )).build();
            }
            Long id = assetFlowService.createAssetFlow(request);
            return Response.status(Response.Status.CREATED)
                    .entity(Map.of(
                            "code", 0,
                            "message", "Asset created successfully",
                            "data", id
                    )).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of(
                            "code", -1,
                            "message", e.getMessage()
                    )).build();
        }
    }

    @PUT
    @Path("/{id}")
    public Response updateAsset(
            @PathParam("id") String id,
            AssetRequest request) {
        
        // TODO: Implement asset update logic
        return Response.ok(Map.of(
                "message", "Asset updated successfully",
                "id", id,
                "data", request
        )).build();
    }

    @DELETE
    @Path("/{id}")
    public Response deleteAsset(@PathParam("id") String id) {
        
        // TODO: Implement asset deletion logic
        return Response.noContent().build();
    }

    // Inner class for request body
}
