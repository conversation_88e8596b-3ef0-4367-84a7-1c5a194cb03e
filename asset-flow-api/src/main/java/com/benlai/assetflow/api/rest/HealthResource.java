package com.benlai.assetflow.api.rest;

import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.time.Instant;
import java.util.Map;

@Path("/api/v1/health")
@Produces(MediaType.APPLICATION_JSON)
public class HealthResource {

    @GET
    public Response health() {
        return Response.ok(Map.of(
                "status", "UP",
                "timestamp", Instant.now().toString(),
                "service", "asset-flow-api",
                "version", "1.0.0-SNAPSHOT",
                "features", Map.of(
                        "rest", "enabled",
                        "grpc", "enabled",
                        "openapi", "enabled"
                )
        )).build();
    }

    @GET
    @Path("/ready")
    public Response ready() {
        // TODO: Add actual readiness checks (database connectivity, etc.)
        return Response.ok(Map.of(
                "status", "READY",
                "timestamp", Instant.now().toString(),
                "checks", Map.of(
                        "database", "UP",
                        "grpc_server", "UP"
                )
        )).build();
    }

    @GET
    @Path("/live")
    public Response live() {
        return Response.ok(Map.of(
                "status", "ALIVE",
                "timestamp", Instant.now().toString()
        )).build();
    }
}
