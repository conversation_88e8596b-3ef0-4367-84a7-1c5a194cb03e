package com.benlai.asset.flow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 资产流水请求 DTO
 * 用于接收客户端的资产流水创建和更新请求
 */

public record AssetRequest(
        @JsonProperty("flow_no")
        @NotBlank(message = "流水号不能为空")
        String flowNo,

        @JsonProperty("company_no")
        String companyNo,

        @JsonProperty("asset_no")
        @NotBlank(message = "资产编号不能为空")
        String assetNo,

        @JsonProperty("asset_type")
        @NotBlank(message = "资产类型不能为空")
        String assetType,

        @JsonProperty("biz_type_code")
        @NotBlank(message = "业务类型代码不能为空")
        String bizTypeCode,

        @JsonProperty("third_biz_type_code")
        String thirdBizTypeCode,

//        @JsonProperty("biz_type_name")
//        String bizTypeName,

        @JsonProperty("paper_no")
        String paperNo,

        @JsonProperty("qty")
        @Positive(message = "数量必须大于0")
        BigDecimal qty,

        @JsonProperty("amount")
        @NotNull(message = "金额不能为空")
        @Positive(message = "金额必须大于0")
        BigDecimal amount,

        @JsonProperty("biz_time")
        String bizTime,

        @JsonProperty("flow_create_time")
        String flowCreateTime
) {
}
