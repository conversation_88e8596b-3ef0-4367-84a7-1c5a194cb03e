<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benlai.basicdata.dao.mapper.ContractSignUserMapper">

    <resultMap id="BaseResultMap" type="com.benlai.basicdata.dao.entity.ContractSignUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="columnName" column="column_name" jdbcType="INTEGER"/>
            <result property="applyCode" column="apply_code" jdbcType="VARCHAR"/>
            <result property="signUserName" column="sign_user_name" jdbcType="VARCHAR"/>
            <result property="signUserTelephone" column="sign_user_telephone" jdbcType="VARCHAR"/>
            <result property="signUserEmail" column="sign_user_email" jdbcType="VARCHAR"/>
            <result property="signUserAddress" column="sign_user_address" jdbcType="VARCHAR"/>
            <result property="signRecBank" column="sign_rec_bank" jdbcType="VARCHAR"/>
            <result property="signRecAccount" column="sign_rec_account" jdbcType="VARCHAR"/>
            <result property="signRecAccountNo" column="sign_rec_account_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,column_name,apply_code,
        sign_user_name,sign_user_telephone,sign_user_email,
        sign_user_address,sign_rec_bank,sign_rec_account,
        sign_rec_account_no
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from contract_sign_user
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from contract_sign_user
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.benlai.basicdata.dao.entity.ContractSignUser" useGeneratedKeys="true">
        insert into contract_sign_user
        ( id,column_name,apply_code
        ,sign_user_name,sign_user_telephone,sign_user_email
        ,sign_user_address,sign_rec_bank,sign_rec_account
        ,sign_rec_account_no)
        values (#{id,jdbcType=INTEGER},#{columnName,jdbcType=INTEGER},#{applyCode,jdbcType=VARCHAR}
        ,#{signUserName,jdbcType=VARCHAR},#{signUserTelephone,jdbcType=VARCHAR},#{signUserEmail,jdbcType=VARCHAR}
        ,#{signUserAddress,jdbcType=VARCHAR},#{signRecBank,jdbcType=VARCHAR},#{signRecAccount,jdbcType=VARCHAR}
        ,#{signRecAccountNo,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.benlai.basicdata.dao.entity.ContractSignUser" useGeneratedKeys="true">
        insert into contract_sign_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="columnName != null">column_name,</if>
                <if test="applyCode != null">apply_code,</if>
                <if test="signUserName != null">sign_user_name,</if>
                <if test="signUserTelephone != null">sign_user_telephone,</if>
                <if test="signUserEmail != null">sign_user_email,</if>
                <if test="signUserAddress != null">sign_user_address,</if>
                <if test="signRecBank != null">sign_rec_bank,</if>
                <if test="signRecAccount != null">sign_rec_account,</if>
                <if test="signRecAccountNo != null">sign_rec_account_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="columnName != null">#{columnName,jdbcType=INTEGER},</if>
                <if test="applyCode != null">#{applyCode,jdbcType=VARCHAR},</if>
                <if test="signUserName != null">#{signUserName,jdbcType=VARCHAR},</if>
                <if test="signUserTelephone != null">#{signUserTelephone,jdbcType=VARCHAR},</if>
                <if test="signUserEmail != null">#{signUserEmail,jdbcType=VARCHAR},</if>
                <if test="signUserAddress != null">#{signUserAddress,jdbcType=VARCHAR},</if>
                <if test="signRecBank != null">#{signRecBank,jdbcType=VARCHAR},</if>
                <if test="signRecAccount != null">#{signRecAccount,jdbcType=VARCHAR},</if>
                <if test="signRecAccountNo != null">#{signRecAccountNo,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.benlai.basicdata.dao.entity.ContractSignUser">
        update contract_sign_user
        <set>
                <if test="columnName != null">
                    column_name = #{columnName,jdbcType=INTEGER},
                </if>
                <if test="applyCode != null">
                    apply_code = #{applyCode,jdbcType=VARCHAR},
                </if>
                <if test="signUserName != null">
                    sign_user_name = #{signUserName,jdbcType=VARCHAR},
                </if>
                <if test="signUserTelephone != null">
                    sign_user_telephone = #{signUserTelephone,jdbcType=VARCHAR},
                </if>
                <if test="signUserEmail != null">
                    sign_user_email = #{signUserEmail,jdbcType=VARCHAR},
                </if>
                <if test="signUserAddress != null">
                    sign_user_address = #{signUserAddress,jdbcType=VARCHAR},
                </if>
                <if test="signRecBank != null">
                    sign_rec_bank = #{signRecBank,jdbcType=VARCHAR},
                </if>
                <if test="signRecAccount != null">
                    sign_rec_account = #{signRecAccount,jdbcType=VARCHAR},
                </if>
                <if test="signRecAccountNo != null">
                    sign_rec_account_no = #{signRecAccountNo,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.benlai.basicdata.dao.entity.ContractSignUser">
        update contract_sign_user
        set 
            column_name =  #{columnName,jdbcType=INTEGER},
            apply_code =  #{applyCode,jdbcType=VARCHAR},
            sign_user_name =  #{signUserName,jdbcType=VARCHAR},
            sign_user_telephone =  #{signUserTelephone,jdbcType=VARCHAR},
            sign_user_email =  #{signUserEmail,jdbcType=VARCHAR},
            sign_user_address =  #{signUserAddress,jdbcType=VARCHAR},
            sign_rec_bank =  #{signRecBank,jdbcType=VARCHAR},
            sign_rec_account =  #{signRecAccount,jdbcType=VARCHAR},
            sign_rec_account_no =  #{signRecAccountNo,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>
