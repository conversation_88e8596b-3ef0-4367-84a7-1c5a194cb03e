package com.benlai.basicdata.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 结算信息
 * @TableName contract_payment
 */
@TableName(value ="contract_payment")
@Data
public class ContractPayment implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请编号
     */
    @TableField(value = "apply_code")
    private String applyCode;

    /**
     * 结算方式
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 是否预付款默认-1 ，否1，是0
     */
    @TableField(value = "is_pre_pay")
    private Integer isPrePay;

    /**
     * 预付款比例
     */
    @TableField(value = "pre_pay_ratio")
    private Integer prePayRatio;

    /**
     * 预付款其他
     */
    @TableField(value = "pre_pay_other")
    private Integer prePayOther;

    /**
     * 预付款金额
     */
    @TableField(value = "pre_pay_amount")
    private BigDecimal prePayAmount;

    /**
     * 货到付款天数
     */
    @TableField(value = "cash_delivery_day")
    private Integer cashDeliveryDay;

    /**
     * 货到付款其他
     */
    @TableField(value = "cash_delivery_other")
    private Integer cashDeliveryOther;

    /**
     * 周期结算
     */
    @TableField(value = "settlement_period")
    private Integer settlementPeriod;

    /**
     * 指定日月结周期结算截止日
     */
    @TableField(value = "payment_day")
    private Integer paymentDay;

    /**
     * 账期
     */
    @TableField(value = "settlement_reconciliation")
    private Integer settlementReconciliation;

    /**
     * 周期结算账期其他
     */
    @TableField(value = "settlement_reconciliation_other")
    private Integer settlementReconciliationOther;

    /**
     * 合同的约定回款形式
     */
    @TableField(value = "pay_collection_way")
    private Integer payCollectionWay;

    /**
     * 约定回款抬头
     */
    @TableField(value = "pay_collection_title")
    private String payCollectionTitle;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0未删除，1已删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    /**
     * 
     */
    @TableField(value = "cash_delivery_day2")
    private Integer cashDeliveryDay2;

    /**
     * 
     */
    @TableField(value = "cash_delivery_other2")
    private Integer cashDeliveryOther2;

    /**
     * 
     */
    @TableField(value = "payment_type")
    private Integer paymentType;

    /**
     * 
     */
    @TableField(value = "final_payment_days")
    private String finalPaymentDays;

    /**
     * 
     */
    @TableField(value = "deliver_days")
    private String deliverDays;

    /**
     * 预付款时间
     */
    @TableField(value = "pre_pay_days")
    private Integer prePayDays;

    @TableField(value = "deposit_amount")
    private BigDecimal depositAmount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}