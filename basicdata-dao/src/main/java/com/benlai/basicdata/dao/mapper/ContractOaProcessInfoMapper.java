package com.benlai.basicdata.dao.mapper;

import com.benlai.basicdata.dao.entity.ContractOaProcessInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
* <AUTHOR>
* @description 针对表【contract_oa_process_info(oa流程信息)】的数据库操作Mapper
* @createDate 2024-12-30 16:15:18
* @Entity com.benlai.basicdata.dao.entity.ContractOaProcessInfo
*/
public interface ContractOaProcessInfoMapper extends BaseMapper<ContractOaProcessInfo> {

    int deleteByPrimaryKey(Long id);

    int insert(ContractOaProcessInfo record);

    int insertSelective(ContractOaProcessInfo record);

    ContractOaProcessInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractOaProcessInfo record);

    int updateByPrimaryKey(ContractOaProcessInfo record);

}
