package com.benlai.basicdata.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName(value = "archives_system")
@Data
public class ArchivesSystemEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 合同编号
     */
    @TableField(value = "compact_no")
    private String compactNo;

    /**
     * 大客户名称
     */
    @TableField(value = "bigcustomer_name")
    private String bigcustomerName;

    /**
     * 大客户行业
     */
    @TableField(value = "customer_industry_no")
    private Integer customerIndustryNo;

    /**
     * 合同金额
     */
    @TableField(value = "compact_amount")
    private BigDecimal compactAmount;

    /**
     * 合同开始时间
     */
    @TableField(value = "compact_start_time")
    private LocalDateTime compactStartTime;

    /**
     * 合同性质
     */
    @TableField(value = "oa_compact_type")
    private Integer oaCompactType;

    /**
     * 下单类别
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 签约主体
     */
    @TableField(value = "company_no")
    private Integer companyNo;

    /**
     * 销售员
     */
    @TableField(value = "salesman_no")
    private Integer salesmanNo;

    /**
     * 销售大区
     */
    @TableField(value = "sales_region")
    private Integer salesRegion;

    /**
     * 销售类型
     */
    @TableField(value = "sales_type")
    private Integer salesType;

    /**
     * 状态
     */
    @TableField(value = "state")
    private Integer status;

    /**
     * 申请人用户id
     */
    @TableField(value = "creator_user_id")
    private Integer creatorUserId;

    /**
     * 更新人用户id
     */
    @TableField(value = "update_user_id")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 0未删除，1已删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}
