package com.benlai.basicdata.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName(value = "archives_product")
@Data
public class ArchivesProductEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 案例库id
     */
    @TableField(value = "archives_id")
    private Integer archivesId;

    /**
     * 大客户行业
     */
    @TableField(value = "product_basic_sysno")
    private Integer productBasicSysno;

    /**
     * 合同编号
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 大类编号
     */
    @TableField(value = "c1_sysno")
    private Integer c1SysNo;

    /**
     * 数量
     */
    @TableField(value = "qty")
    private Integer qty;

    /**
     * 申请人用户id
     */
    @TableField(value = "creator_user_id")
    private Integer creatorUserId;

    /**
     * 更新人用户id
     */
    @TableField(value = "update_user_id")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 0未删除，1已删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
}
