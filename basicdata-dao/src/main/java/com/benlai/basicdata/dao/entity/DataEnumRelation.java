package com.benlai.basicdata.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 指定项连联动关系
 * @TableName data_enum_relation
 */
@TableName(value ="data_enum_relation")
@Data
public class DataEnumRelation implements Serializable {
    /**
     * 关系ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "parent_data_enum_id")
    private Integer parentDataEnumId;

    /**
     * 
     */
    @TableField(value = "parent_item_code")
    private String parentItemCode;

    /**
     * Json关系描述
     */
    @TableField(value = "relations")
    private Integer relations;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DataEnumRelation other = (DataEnumRelation) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getParentDataEnumId() == null ? other.getParentDataEnumId() == null : this.getParentDataEnumId().equals(other.getParentDataEnumId()))
            && (this.getParentItemCode() == null ? other.getParentItemCode() == null : this.getParentItemCode().equals(other.getParentItemCode()))
            && (this.getRelations() == null ? other.getRelations() == null : this.getRelations().equals(other.getRelations()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getParentDataEnumId() == null) ? 0 : getParentDataEnumId().hashCode());
        result = prime * result + ((getParentItemCode() == null) ? 0 : getParentItemCode().hashCode());
        result = prime * result + ((getRelations() == null) ? 0 : getRelations().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", parentDataEnumId=").append(parentDataEnumId);
        sb.append(", parentItemCode=").append(parentItemCode);
        sb.append(", relations=").append(relations);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}