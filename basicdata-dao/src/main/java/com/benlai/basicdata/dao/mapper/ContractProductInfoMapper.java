package com.benlai.basicdata.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.benlai.basicdata.dao.entity.ContractProductInfo;

/**
* <AUTHOR>
* @description 针对表【contract_product_info(合同商品信息)】的数据库操作Mapper
* @createDate 2024-12-20 15:30:46
* @Entity com.benlai.basicdata.dao.entity.ContractProductInfo
*/
public interface ContractProductInfoMapper extends BaseMapper<ContractProductInfo> {

    int deleteByPrimaryKey(Long id);

    int insert(ContractProductInfo record);

    int insertSelective(ContractProductInfo record);

    ContractProductInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractProductInfo record);

    int updateByPrimaryKey(ContractProductInfo record);

}
