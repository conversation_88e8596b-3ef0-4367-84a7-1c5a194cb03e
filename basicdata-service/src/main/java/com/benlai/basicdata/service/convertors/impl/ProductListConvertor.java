package com.benlai.basicdata.service.convertors.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.benlai.basicdata.client.vo.CreateContactReq;
import com.benlai.basicdata.dao.entity.ContractProducList;
import com.benlai.basicdata.dao.mapper.ContractProducListMapper;
import com.benlai.basicdata.models.dtos.ProducInfoForListDto;
import com.benlai.basicdata.service.Enums.ConvertTypes;
import com.benlai.basicdata.service.convertors.IConvertor;
import com.benlai.common.exception.DefaultException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ProductListConvertor implements IConvertor<List<ProducInfoForListDto>, List<ContractProducList>> {
    @Getter
    private List<ProducInfoForListDto> productList;

    private List<ContractProducList> contractProductList;

    private String applyCode;

    @Override
    public List<ProducInfoForListDto> parseData(Map<String, Object> inputs) {
        if(inputs.containsKey(ConvertTypes.PRODUCT_LIST.getFieldName())){
            Object obj = inputs.get(ConvertTypes.PRODUCT_LIST.getFieldName());
            if(obj instanceof String){
                this.productList = JSON.parseArray((String)obj, ProducInfoForListDto.class);
            }else if(obj  != null){
                this.productList = JSON.parseArray(JSON.toJSONString(obj), ProducInfoForListDto.class);
            }
            return this.productList;
        }
        return List.of();
    }

    @Override
    public Boolean checkData(Integer actionType, List<Integer> templateTypes) {
       if(actionType == 0){
           return true;
       }
        return true;
    }

    @Override
    public List<ContractProducList> toEntity(String applyCode, List<ContractProducList> pl) {
        this.applyCode = applyCode;
         this.contractProductList = pl == null ? new ArrayList<>() : pl;
         for(ProducInfoForListDto dto: this.productList){
             ContractProducList cpl = new ContractProducList();
             cpl.setApplyCode(applyCode);
             cpl.setName(dto.getName());
             cpl.setSpecif(dto.getSpecif());
             cpl.setAmount(dto.getAmount());
             cpl.setPrice(dto.getPrice());
             cpl.setAccount(dto.getAccount());
             cpl.setValidInfo(dto.getValidInfo());
             cpl.setCreditAmount(dto.getCreditAmount());
             if(dto.getProductFormat() != null){
                 cpl.setProductFormat(Integer.valueOf(dto.getProductFormat()));
             }
             if(dto.getDeliveryFormat() != null){
                 cpl.setDeliveryFormat(Integer.valueOf(dto.getDeliveryFormat()));
             }


             this.contractProductList.add(cpl);

         }
        return this.contractProductList;
    }

    @Override
    public List<ContractProducList> save(Boolean updated) {

        ContractProducListMapper mapper = SpringUtil.getBean(ContractProducListMapper.class);
        if(mapper == null)
            throw new DefaultException(-1, "");

        mapper.deleteByApplyCode(this.applyCode);
        if(!CollectionUtils.isEmpty(this.contractProductList)){
            for(ContractProducList cpl: this.contractProductList){
                mapper.insert(cpl);
            }
        }

        return this.contractProductList;
    }

    @Override
    public void setOaRequest(CreateContactReq req){}
}
