package com.benlai.basicdata.service.processor;

import com.benlai.basicdata.models.dtos.CreateContractDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public abstract class IProcessor {

    private final static Logger logger = LoggerFactory.getLogger(IProcessor.class);

    public abstract String getDocxUrlName();

    public abstract String getDocxName(CreateContractDto contractDto);

    /**
     * 获取TplDocx地址
     *
     * @return
     */
    public abstract String getTplDocx();

    /**
     * 获取TplMap
     *
     * @return
     */
    public abstract Map<String, String> getTplMap();

    /**
     * 模型拼装数据JSON格式
     *
     * @param contractDto
     * @return
     */
    public abstract String getJsonData(CreateContractDto contractDto);
}
