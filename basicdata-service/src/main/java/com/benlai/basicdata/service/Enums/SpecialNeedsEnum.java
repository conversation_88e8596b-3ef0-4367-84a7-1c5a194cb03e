package com.benlai.basicdata.service.Enums;

public enum SpecialNeedsEnum {
    UNKNOWN(-999999, "-"),
    NO_REQUIREMENT(2, "无要求"),
    SHOW_AMOUNT(4, "显示金额"),
    SHOW_QUANTITY(8, "显示数量"),
    UPLOAD_INVOICE(16, "下载发票"),

    NOTICE_OF_WINNING_THE_BID(32, "下载中标通知书");

    private final int code;
    private final String description;

    SpecialNeedsEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 通过 code 获取枚举实例
    public static SpecialNeedsEnum getByCode(int code) {
        for (SpecialNeedsEnum type : SpecialNeedsEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return SpecialNeedsEnum.UNKNOWN;
    }

    // 通过 description 获取枚举实例
    public static SpecialNeedsEnum getByDescription(String description) {
        for (SpecialNeedsEnum type : SpecialNeedsEnum.values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return SpecialNeedsEnum.UNKNOWN;
    }
}
