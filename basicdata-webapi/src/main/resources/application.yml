app:
  id: ${spring.application.name}
server:
  port: ${APP_HTTP_PORT:8080}
spring:
  jackson:
    default-property-inclusion: always

  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: business-basicdata-service
  profiles:
    active: ${APP_ENVIRONMENT:local}
  config:
    import:
      - apollo://application
      - classpath:application-local.yml
      - classpath:application-branch.yml
      - classpath:application-trunk.yml
      - classpath:application-prepare.yml
      - classpath:application-online.yml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************
    username: aliyun_admin
    password: Retail2021@BLX
  data:
    redis:
      host: branch-cache.redis.rds.aliyuncs.com
      port: 6379
      password: 'blshBranch2023'
  cloud:
    openfeign:
      client:
        config:
          oa-service:
            connect-timeout: 180000  # 单位毫秒
            read-timeout: 180000     # 单位毫秒


soa:
  enabled: true
response:
  value: true
  data: false

feign:
  okhttp:
    enabled: true

oa:
  host: https://oa-test.benlai.com
  appid: E92A515C-6EB5-4D37-A09E-1B295A4A9C72
  systemid: erp
  pwd: 123456

logging:
  level:
    com.baomidou.mybatisplus: info

mybatis-plus:
  global-config:
    field-strategy: 0
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

