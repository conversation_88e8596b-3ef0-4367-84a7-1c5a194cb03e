package com.benlai.basicdata.client.dto.response;

import lombok.Data;

@Data
public class CategorySOAResponse {
    /**
     * 获取或设置 SysNo 属性值
     */
    private Integer sysNo = 0;

    /**
     * 获取或设置 m_ID 属性值
     */
    private String id = "";

    /**
     * 获取或设置 Name 属性值
     */
    private String name = "";

    /**
     * 获取或设置 Level 属性值
     */
    private Integer level = 0;

    /**
     * 获取或设置 ParentSysNo 属性值
     */
    private Integer parentSysNo = 0;

    /**
     * 获取或设置 Status 属性值
     */
    private Integer status = 0;

    /**
     * 获取或设置 IsShow 属性值
     */
    private Integer isShow = 0;

    /**
     * 获取或设置 MaxSKUNum 大类目限制SKU数量属性值
     */
    private Integer maxSKUNum = 0;
}
