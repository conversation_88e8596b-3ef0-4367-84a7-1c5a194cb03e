package com.benlai.basicdata.client.workflow;

import lombok.Data;

import java.util.List;

@Data
public class IncidentDto {
    /** 流程实例id */
    private Long incidentId;
    private Integer groupId;
    private Integer processId;
    private String processName;
    private String applyNo;
    private Integer applyUserId;
    private String applyUserName;
    private String applyDeptId;
    private String applyTime;
    private Integer auditUserId;
    private String auditUserName;
    private String auditTime;
    private Integer status;
    private String statusName;
    private String Remark;


    /**
     * 关联对象编号 Long【必填】
     */
    private Long referId;
    /**
     * 关联对象名称 （refer_name）600 【非必填】
     */
    private String referName;
    /**
     * 关联对象内容1 （refer_data1）200【非必填】
     */
    private String referData1;
    /**
     * 关联对象内容2 （refer_data2）200【非必填】
     */
    private String referData2;
    /**
     * 关联对象内容3 （refer_data3）200【非必填】
     */
    private String referData3;
    /**
     * 关联对象内容4 （refer_data4）500【非必填】
     */
    private String referData4;
    /**
     * 关联对象位运算 （用来存储各种Boolean字段值）【非必填】
     */
    private Long bitFlag;

    /**
     * 展示内容【非必填】
     */
    private List<LabelValueDto> showContents;

    /**
     * 有变更前后的内容【非必填】
     */
    private List<LabelValueChangeDto> changeContents;
}
