package com.benlai.basicdata.client;

import com.benlai.basicdata.client.dto.request.ContractSearchSOARequest;
import com.benlai.basicdata.client.dto.response.ContractSearchSOAResponse;
import com.benlai.basicdata.client.dto.response.ProductBasicOfB2BResponse;
import com.benlai.basicdata.client.feign.ErpCompactFeignClient;
import com.benlai.basicdata.client.feign.ErpProductFeignClient;
import com.benlai.basicdata.client.vo.PageResult;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ErpProductClient {
    @Resource
    private ErpProductFeignClient erpProductFeignClient;

    public List<ProductBasicOfB2BResponse> getBasicList(List<Integer> productBasicSysNoList){
        HashMap<String, Object> map = new HashMap<>();
        map.put("productBasicSysNoList", productBasicSysNoList);
        return erpProductFeignClient.getBasicList(map);
    }

    public Map<String, String> GetDicByBasicFirstImage(List<Integer> productBasicSysNoList, Integer productType){
        HashMap<String, Object> map = new HashMap<>();
        map.put("productBasicSysNoList", productBasicSysNoList);
        map.put("productType", productType);
        return erpProductFeignClient.GetDicByBasicFirstImage(map);
    }


}
