package com.benlai.basicdata.client.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PurchasePurposeSOAResponse {
    private Integer sysNo;
    private String dicFlag;
    private Integer dicNo;
    private Integer parentNo;
    private String dicName;
    private String dicOrder;
    private Integer status;
    private String description;
    private String createUserNo;
    private LocalDateTime createTime;
    private String updateUserNo;
    private LocalDateTime updateTime;
    private Integer order;
}
