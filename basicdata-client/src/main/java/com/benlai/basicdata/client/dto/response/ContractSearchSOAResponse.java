package com.benlai.basicdata.client.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ContractSearchSOAResponse {
    /**
     * 编号
     */

    private Integer sysNo;

    /**
     * 流程编号
     */

    private String compactNo;

    /**
     * 下单类别
     */

    private Integer orderType;

    /**
     * OA合同性质
     */

    private Integer oACompactType;

    /**
     * 业务类型
     */

    private Integer salesType;

    /**
     * 客户编号
     */

    private Integer bigCustomerSysNo;

    /**
     * 客户名称
     */

    private String bigCustomerName;

    /**
     * 销售员编号
     */

    private Integer applyUserSysNo;

    /**
     * 销售员名称
     */

    private String applyUserName;

    /**
     * 部门编号
     */

    private Integer applyDeptSysNo;

    /**
     * 部门名称
     */

    private String applyDeptName;

    /**
     * 合同总金额
     */

    private BigDecimal amount;

    /**
     * 累计业绩金额
     */

    private BigDecimal totalOrderAmount;

    /**
     * 浮动比例
     */

    private BigDecimal rate;

    /**
     * 起始期限
     */

    private LocalDateTime beginTime;

    /**
     * 结束期限
     */

    private LocalDateTime endTime;

    /**
     * 约定回款日期
     */

    private LocalDateTime receivePayTime;

    /**
     * 关联父类编号
     */

    private Integer parentSysNo;

    /**
     * 关联父类流程编号
     */

    private String parentCompactNo;

    /**
     * OA合同编号
     */

    private String oAID;

    /**
     * 流程状态
     */

    private Integer status;

    /**
     * 是否关联业绩单
     */

    private boolean IsRelateOrder;
    /**
     * 合同性质
     */

    private String OACompactTypeName;
    /**
     * 销售类型名称
     */

    private String salesTypeName;

    /**
     * 合同名称
     */

    private String title;
}
